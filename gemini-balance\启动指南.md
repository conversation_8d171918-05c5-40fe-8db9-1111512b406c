# Gemini Balance 启动指南

## 项目简介
Gemini Balance 是一个基于 Python FastAPI 构建的 Google Gemini API 代理和负载均衡器，支持多个 API Key 的轮询使用和 OpenAI 格式的 API 兼容。

## 已完成的准备工作
✅ 项目已克隆到: `d:\xx\gemini-balance`  
✅ 已创建基础 `.env` 配置文件  
✅ 项目结构已就绪  

## 启动方式

### 方式一：Docker Compose 启动（推荐）
如果您的系统已安装 Docker 和 Docker Compose：

```bash
# 在项目目录下运行
docker-compose up -d

# 查看容器状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 方式二：本地 Python 环境启动

#### 1. 安装 Python 依赖
```bash
# 使用国内镜像源安装（推荐）
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者使用默认源
pip install -r requirements.txt
```

#### 2. 配置环境变量
编辑 `.env` 文件，**必须配置以下关键项**：

```env
# 必填：Gemini API Keys（至少需要一个有效的 API Key）
API_KEYS=["your-real-gemini-api-key-here"]

# 必填：访问令牌（用于API认证）
ALLOWED_TOKENS=["sk-your-custom-token"]
AUTH_TOKEN=sk-your-custom-token

# 数据库配置（如使用 SQLite，可简化配置）
DATABASE_TYPE=sqlite
SQLITE_DATABASE=./data/gemini_balance.db
```

#### 3. 启动应用
```bash
# 使用 uvicorn 启动
uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

# 或者直接运行（如果环境配置正确）
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## 配置说明

### 重要配置项
- `API_KEYS`: Gemini API 密钥列表，支持多个 Key 负载均衡
- `ALLOWED_TOKENS`: 允许访问的令牌列表，用于API认证
- `DATABASE_TYPE`: 数据库类型，可选 `sqlite` 或 `mysql`
- `TEST_MODEL`: 用于测试 API Key 可用性的模型

### 可选功能配置
- `IMAGE_MODELS`: 支持图像生成的模型列表
- `SEARCH_MODELS`: 支持联网搜索的模型列表
- `PROXIES`: 代理服务器配置（如需要）

## 访问地址
启动成功后，可通过以下地址访问：

- **主页**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **Key 状态监控**: http://localhost:8000/keys_status（需要认证）
- **OpenAI 兼容接口**: http://localhost:8000/v1
- **Gemini 原生接口**: http://localhost:8000/gemini/v1beta

## API 使用示例

### OpenAI 格式调用
```bash
curl -X POST "http://localhost:8000/v1/chat/completions" \
  -H "Authorization: Bearer sk-your-custom-token" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "gemini-1.5-flash",
    "messages": [{"role": "user", "content": "Hello!"}]
  }'
```

### Gemini 原生格式调用
```bash
curl -X POST "http://localhost:8000/gemini/v1beta/models/gemini-1.5-flash:generateContent" \
  -H "Authorization: Bearer sk-your-custom-token" \
  -H "Content-Type: application/json" \
  -d '{
    "contents": [{"parts": [{"text": "Hello!"}]}]
  }'
```

## 故障排除

### 常见问题
1. **依赖安装失败**: 尝试使用国内镜像源或检查网络连接
2. **API Key 无效**: 确保在 `.env` 文件中配置了有效的 Gemini API Key
3. **端口被占用**: 修改启动命令中的端口号
4. **数据库连接失败**: 检查数据库配置或使用 SQLite 简化配置

### 日志查看
- 应用日志会显示在终端中
- 详细的错误日志可在管理后台查看
- 使用 `--reload` 参数可以在代码修改时自动重启

## 下一步
1. 获取有效的 Gemini API Key 并更新 `.env` 文件
2. 根据需要调整其他配置项
3. 启动应用并测试 API 接口
4. 访问管理后台进行进一步配置

---
**注意**: 请确保妥善保管您的 API Key 和访问令牌，不要将其提交到版本控制系统中。
