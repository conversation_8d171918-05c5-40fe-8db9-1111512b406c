#!/usr/bin/env python3
"""
简化的启动脚本，用于快速启动 Gemini Balance 服务
"""

import os
import sys
import subprocess
import time

def check_python_version():
    """检查 Python 版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要 Python 3.8 或更高版本")
        sys.exit(1)
    print(f"✓ Python 版本: {sys.version}")

def install_basic_deps():
    """安装基础依赖"""
    basic_deps = [
        "fastapi",
        "uvicorn",
        "python-dotenv",
        "pydantic",
        "pydantic-settings",
        "requests",
        "httpx",
        "jinja2",
        "python-multipart"
    ]
    
    print("正在安装基础依赖...")
    for dep in basic_deps:
        try:
            __import__(dep.replace('-', '_'))
            print(f"✓ {dep} 已安装")
        except ImportError:
            print(f"正在安装 {dep}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", dep], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✓ {dep} 安装成功")
            else:
                print(f"✗ {dep} 安装失败: {result.stderr}")

def create_minimal_env():
    """创建最小化的环境配置"""
    env_content = """# 最小化配置
DATABASE_TYPE=sqlite
SQLITE_DATABASE=data/default_db.sqlite
API_KEYS=["your-api-key-here"]
ALLOWED_TOKENS=["sk-123456"]
AUTH_TOKEN=sk-123456
TEST_MODEL=gemini-1.5-flash
LOG_LEVEL=info
"""
    
    if not os.path.exists('.env'):
        with open('.env', 'w', encoding='utf-8') as f:
            f.write(env_content)
        print("✓ 创建了最小化的 .env 配置文件")
    else:
        print("✓ .env 文件已存在")

def create_data_dir():
    """创建数据目录"""
    if not os.path.exists('data'):
        os.makedirs('data')
        print("✓ 创建了 data 目录")
    else:
        print("✓ data 目录已存在")

def start_server():
    """启动服务器"""
    print("\n正在启动 Gemini Balance 服务...")
    print("服务地址: http://localhost:8000")
    print("按 Ctrl+C 停止服务")

    # 确保在正确的目录下运行
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    print(f"当前工作目录: {os.getcwd()}")

    try:
        # 尝试启动服务
        subprocess.run([
            sys.executable, "-m", "uvicorn",
            "app.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload"
        ], cwd=script_dir)
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"启动失败: {e}")
        print("\n请检查依赖是否完整安装，或尝试使用 Docker 方式启动")

def main():
    """主函数"""
    print("=== Gemini Balance 快速启动脚本 ===\n")
    
    check_python_version()
    install_basic_deps()
    create_minimal_env()
    create_data_dir()
    start_server()

if __name__ == "__main__":
    main()
