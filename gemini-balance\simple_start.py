#!/usr/bin/env python3
"""
简化版启动脚本 - 绕过复杂依赖
"""

import os
import sys
import json
from typing import List, Dict, Any

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from fastapi import FastAPI, HTTPException, Depends, Request
    from fastapi.responses import JSONResponse, HTMLResponse
    from fastapi.security import HTTP<PERSON>earer, HTTPAuthorizationCredentials
    import uvicorn
    print("✅ 基础依赖包已安装")
except ImportError as e:
    print(f"❌ 缺少基础依赖: {e}")
    print("请先安装: pip install fastapi uvicorn")
    sys.exit(1)

# 简化的配置类
class SimpleConfig:
    def __init__(self):
        # 从环境变量或.env文件读取配置
        self.load_env()
        
        # 基础配置
        self.API_KEYS = json.loads(os.getenv('API_KEYS', '[]'))
        self.ALLOWED_TOKENS = json.loads(os.getenv('ALLOWED_TOKENS', '["sk-123456"]'))
        self.AUTH_TOKEN = os.getenv('AUTH_TOKEN', 'sk-123456')
        self.BASE_URL = os.getenv('BASE_URL', 'https://generativelanguage.googleapis.com/v1beta')
        
        print(f"📋 配置加载完成:")
        print(f"   API Keys: {len(self.API_KEYS)} 个")
        print(f"   允许的Token: {len(self.ALLOWED_TOKENS)} 个")
    
    def load_env(self):
        """加载.env文件"""
        env_file = '.env'
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ .env 文件加载完成")
        else:
            print("⚠️  未找到 .env 文件")

# 创建配置实例
config = SimpleConfig()

# 创建FastAPI应用
app = FastAPI(
    title="Gemini Balance - 简化版",
    description="Google Gemini API 代理和负载均衡器",
    version="1.0.0"
)

# 安全认证
security = HTTPBearer()

def verify_token(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """验证访问令牌"""
    token = credentials.credentials
    if token not in config.ALLOWED_TOKENS:
        raise HTTPException(status_code=401, detail="Invalid token")
    return token

@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Gemini Balance API - 简化版",
        "status": "running",
        "version": "1.0.0",
        "endpoints": {
            "health": "/health",
            "models": "/v1/models", 
            "chat": "/v1/chat/completions",
            "docs": "/docs"
        }
    }

@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "healthy",
        "api_keys_count": len(config.API_KEYS),
        "tokens_count": len(config.ALLOWED_TOKENS)
    }

@app.get("/v1/models")
async def list_models(token: str = Depends(verify_token)):
    """列出可用模型"""
    models = [
        {
            "id": "gemini-1.5-flash",
            "object": "model",
            "created": **********,
            "owned_by": "google"
        },
        {
            "id": "gemini-1.5-pro",
            "object": "model", 
            "created": **********,
            "owned_by": "google"
        },
        {
            "id": "gemini-2.0-flash-exp",
            "object": "model",
            "created": **********,
            "owned_by": "google"
        }
    ]
    
    return {
        "object": "list",
        "data": models
    }

@app.post("/v1/chat/completions")
async def chat_completions(request: Request, token: str = Depends(verify_token)):
    """聊天补全接口"""
    try:
        data = await request.json()
        
        # 检查是否有可用的API Key
        if not config.API_KEYS:
            raise HTTPException(status_code=500, detail="No API keys configured")
        
        # 这里应该调用真实的Gemini API
        # 目前返回模拟响应
        return {
            "id": "chatcmpl-123",
            "object": "chat.completion",
            "created": 1677652288,
            "model": data.get("model", "gemini-1.5-flash"),
            "choices": [{
                "index": 0,
                "message": {
                    "role": "assistant",
                    "content": "这是一个简化版的Gemini Balance服务。要使用完整功能，请安装所有依赖包并配置真实的API Key。"
                },
                "finish_reason": "stop"
            }],
            "usage": {
                "prompt_tokens": 10,
                "completion_tokens": 20,
                "total_tokens": 30
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status", response_class=HTMLResponse)
async def status_page():
    """状态页面"""
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <title>Gemini Balance - 状态</title>
        <meta charset="utf-8">
        <style>
            body {{ font-family: Arial, sans-serif; margin: 40px; }}
            .status {{ background: #f0f8ff; padding: 20px; border-radius: 8px; }}
            .config {{ background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }}
        </style>
    </head>
    <body>
        <h1>🚀 Gemini Balance - 简化版</h1>
        
        <div class="status">
            <h2>📊 服务状态</h2>
            <p>✅ 服务运行中</p>
            <p>📡 API Keys: {len(config.API_KEYS)} 个</p>
            <p>🔑 访问令牌: {len(config.ALLOWED_TOKENS)} 个</p>
        </div>
        
        <div class="config">
            <h2>⚙️ 配置信息</h2>
            <p><strong>Base URL:</strong> {config.BASE_URL}</p>
            <p><strong>API Keys:</strong> {'已配置' if config.API_KEYS else '未配置'}</p>
        </div>
        
        <div class="config">
            <h2>📖 使用说明</h2>
            <p>这是一个简化版本，用于测试基本功能。</p>
            <p>要使用完整功能，请:</p>
            <ul>
                <li>安装所有依赖: <code>pip install -r requirements.txt</code></li>
                <li>配置真实的Gemini API Key</li>
                <li>使用完整版启动: <code>uvicorn app.main:app --reload</code></li>
            </ul>
        </div>
        
        <div class="config">
            <h2>🔗 API 端点</h2>
            <ul>
                <li><a href="/docs">API 文档</a></li>
                <li><a href="/health">健康检查</a></li>
                <li><a href="/v1/models">模型列表</a></li>
            </ul>
        </div>
    </body>
    </html>
    """
    return html_content

if __name__ == "__main__":
    print("\n🚀 启动 Gemini Balance 简化版...")
    print("📍 访问地址: http://localhost:8000")
    print("📖 API文档: http://localhost:8000/docs")
    print("📊 状态页面: http://localhost:8000/status")
    print("\n按 Ctrl+C 停止服务\n")
    
    uvicorn.run(
        "simple_start:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
