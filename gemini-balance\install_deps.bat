@echo off
echo 正在安装 Gemini Balance 依赖包...

REM 清除代理设置
set HTTP_PROXY=
set HTTPS_PROXY=
set ALL_PROXY=

echo 尝试使用不同的镜像源安装依赖...

REM 尝试阿里云镜像
echo 尝试阿里云镜像...
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/ --trusted-host mirrors.aliyun.com
if %errorlevel% == 0 goto success

REM 尝试清华镜像
echo 尝试清华镜像...
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/ --trusted-host pypi.tuna.tsinghua.edu.cn
if %errorlevel% == 0 goto success

REM 尝试豆瓣镜像
echo 尝试豆瓣镜像...
pip install -r requirements.txt -i https://pypi.douban.com/simple/ --trusted-host pypi.douban.com
if %errorlevel% == 0 goto success

REM 尝试华为镜像
echo 尝试华为镜像...
pip install -r requirements.txt -i https://mirrors.huaweicloud.com/repository/pypi/simple/ --trusted-host mirrors.huaweicloud.com
if %errorlevel% == 0 goto success

REM 尝试默认源
echo 尝试默认源...
pip install -r requirements.txt --no-proxy
if %errorlevel% == 0 goto success

echo 所有镜像源都失败，尝试逐个安装关键包...

REM 逐个安装关键包
pip install fastapi --no-proxy
pip install uvicorn --no-proxy
pip install pydantic-settings --no-proxy
pip install sqlalchemy --no-proxy
pip install databases[aiosqlite] --no-proxy
pip install python-dotenv --no-proxy
pip install httpx --no-proxy
pip install jinja2 --no-proxy

:success
echo 依赖安装完成！
echo 现在可以运行: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
pause
