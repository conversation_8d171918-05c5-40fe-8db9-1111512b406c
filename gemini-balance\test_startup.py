#!/usr/bin/env python3
"""
Gemini Balance 启动测试脚本
用于验证项目依赖和基本配置是否正确
"""

import sys
import os
import importlib.util

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print("   ❌ Python版本过低，需要Python 3.9+")
        return False
    else:
        print("   ✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查关键依赖包"""
    print("\n📦 检查依赖包...")
    
    required_packages = [
        'fastapi',
        'uvicorn', 
        'pydantic',
        'httpx',
        'sqlalchemy',
        'python_dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            # 尝试导入包
            if package == 'python_dotenv':
                import dotenv
            else:
                __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (未安装)")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装:")
        print("pip install -r requirements.txt")
        return False
    else:
        print("   ✅ 所有关键依赖包已安装")
        return True

def check_env_file():
    """检查环境配置文件"""
    print("\n⚙️  检查配置文件...")
    
    env_file = ".env"
    if not os.path.exists(env_file):
        print(f"   ❌ 未找到 {env_file} 文件")
        return False
    
    print(f"   ✅ 找到 {env_file} 文件")
    
    # 检查关键配置项
    try:
        from dotenv import load_dotenv
        load_dotenv()
        
        api_keys = os.getenv('API_KEYS', '[]')
        allowed_tokens = os.getenv('ALLOWED_TOKENS', '[]')
        
        if api_keys == '[]' or 'your-gemini-api-key-here' in api_keys:
            print("   ⚠️  API_KEYS 需要配置真实的 Gemini API Key")
        else:
            print("   ✅ API_KEYS 已配置")
            
        if allowed_tokens == '[]' or 'sk-123456' in allowed_tokens:
            print("   ⚠️  建议修改默认的 ALLOWED_TOKENS")
        else:
            print("   ✅ ALLOWED_TOKENS 已配置")
            
    except Exception as e:
        print(f"   ⚠️  配置文件读取异常: {e}")
    
    return True

def check_app_structure():
    """检查应用结构"""
    print("\n📁 检查应用结构...")
    
    required_paths = [
        'app',
        'app/main.py',
        'app/core',
        'app/config',
        'requirements.txt'
    ]
    
    for path in required_paths:
        if os.path.exists(path):
            print(f"   ✅ {path}")
        else:
            print(f"   ❌ {path} (缺失)")
            return False
    
    return True

def test_basic_import():
    """测试基本导入"""
    print("\n🔍 测试应用导入...")
    
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.getcwd())
        
        # 尝试导入应用配置
        from app.config.config import Settings
        print("   ✅ 配置模块导入成功")
        
        # 尝试创建配置实例
        settings = Settings()
        print("   ✅ 配置实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Gemini Balance 启动检查")
    print("=" * 50)
    
    checks = [
        check_python_version(),
        check_dependencies(), 
        check_env_file(),
        check_app_structure(),
        test_basic_import()
    ]
    
    print("\n" + "=" * 50)
    
    if all(checks):
        print("🎉 所有检查通过！可以尝试启动应用:")
        print("   uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
    else:
        print("❌ 部分检查未通过，请根据上述提示解决问题")
        
    print("\n📖 详细启动指南请查看: 启动指南.md")

if __name__ == "__main__":
    main()
